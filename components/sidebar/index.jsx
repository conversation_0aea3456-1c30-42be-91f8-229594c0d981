import Link from "next/link";
import { useEffect, useState } from "react";
import MenuItem from "./MenuItem";
import { useTableContext } from "../context/TableContext";
import { FaChevronDown } from "react-icons/fa";
import { useRouter } from "next/router";
import use<PERSON>angHook from "./SidebarLinks";
import { langs } from "./../locale";
import { useLocaleContext } from "../context/LocaleContext";
import { useUserContext } from "../context/UserContext";
import apiClientProtected from "../../helpers/apiClient";

function Sidebar({ mode, showSidebar, setOpenSidebar }) {
  const router = useRouter();
  const { user } = useUserContext();
  const { locale } = useLocaleContext();
  const {
    shrinkSidebar,
    setShrinkSidebar,
    shrinkSidebarOnHover,
    setShrinkSidebarOnHover,
    pageInfo,
  } = useTableContext();

  const { links } = useLangHook();

  const [linkList, setLinkList] = useState([]);
  const [messageCount, setMessageCount] = useState(null);

  useEffect(() => {
    if (user && user.permissions) {
      const validPermissions = (item) =>
        user.permissions.includes(item.permissions);
      // console.log(validPermissions(), "frrrrrrrrrrrrrrrrrrrrrrrrrrr");

      const thisWillWork = (arr) => {
        return arr.reduce(
          (acc, item) => {
            // acc -> short for "accumulator" (array)
            // item -> the current array item

            // so that we don't overwrite the item parameter
            const newItem = item;

            if (item.subMenu) {
              // here is the recursive call
              newItem.subMenu = thisWillWork(item.subMenu);
            }
            if (validPermissions(newItem)) {
              // here's where acc takes the new item
              acc.push(newItem);
            }
            // we always have to return acc
            return acc;
          },
          // initialize accumulator (empty array)
          []
        );
      };

      // console.log(thisWillWork(links), "Judo");
      setLinkList(thisWillWork(links));
    }
  }, [locale]);

  useEffect(() => {
    if (apiClientProtected()) {
      (async () => {
        const response = await apiClientProtected().get(
          "/messages/unreadCount"
        );
        setMessageCount(response.data.count);
      })();
    }
  }, [pageInfo]);

  const getSidebarHover = () => {
    setShrinkSidebar(!shrinkSidebar);
    setTimeout(() => setShrinkSidebarOnHover(!shrinkSidebarOnHover), 800);
  };

  const toggleAccordion = (id) => {
    const newLinks = linkList.map((item) => {
      if (id !== item.id) {
        item.isMenuOpen = false;
      } else {
        item.isMenuOpen = !item.isMenuOpen;
      }
      return item;
    });

    setLinkList(newLinks);
  };

  return (
    <div
      className={`aside aside-dark aside-hoverable ${
        shrinkSidebar && "sidebar__shrink"
      } ${shrinkSidebarOnHover && "sidebar__hover"}`}
    >
      <div className="aside-logo flex-column-auto" id="kt_aside_logo">
        {!shrinkSidebar && (
          <Link href="/admin">
            <a className="d-flex text-decoration-none">
              <div
                style={{
                  display: "flex",
                }}
              >
                <img
                  alt="Logo"
                  src="/assets/media/logos/logo.png"
                  className="h-40px logo"
                />
                <h5
                  className="logo-txt logo"
                  style={{ fontSize: locale === "en" ? "12px" : "auto" }}
                >
                  {locale && langs[locale]["logo_title"]}
                </h5>
              </div>
            </a>
          </Link>
        )}

        <div
          className="btn btn-icon w-auto px-0 btn-active-color-primary aside-toggle"
          onClick={getSidebarHover}
          style={{ transform: `rotate(${shrinkSidebar ? "180deg" : 0})` }}
        >
          <span className="svg-icon svg-icon-1 rotate-180">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
            >
              <path
                opacity="0.5"
                d="M14.2657 11.4343L18.45 7.25C18.8642 6.83579 18.8642 6.16421 18.45 5.75C18.0358 5.33579 17.3642 5.33579 16.95 5.75L11.4071 11.2929C11.0166 11.6834 11.0166 12.3166 11.4071 12.7071L16.95 18.25C17.3642 18.6642 18.0358 18.6642 18.45 18.25C18.8642 17.8358 18.8642 17.1642 18.45 16.75L14.2657 12.5657C13.9533 12.2533 13.9533 11.7467 14.2657 11.4343Z"
                fill="black"
              />
              <path
                d="M8.2657 11.4343L12.45 7.25C12.8642 6.83579 12.8642 6.16421 12.45 5.75C12.0358 5.33579 11.3642 5.33579 10.95 5.75L5.40712 11.2929C5.01659 11.6834 5.01659 12.3166 5.40712 12.7071L10.95 18.25C11.3642 18.6642 12.0358 18.6642 12.45 18.25C12.8642 17.8358 12.8642 17.1642 12.45 16.75L8.2657 12.5657C7.95328 12.2533 7.95328 11.7467 8.2657 11.4343Z"
                fill="black"
              />
            </svg>
          </span>
        </div>
      </div>

      {/*end::Brand*/}
      {/*begin::Aside menu*/}
      <div className="aside-menu flex-column-fluid">
        {/*begin::Aside Menu*/}
        <div
          className="hover-scroll-overlay-y mt-4"
          style={{ marginBottom: "8rem" }}
          id="kt_aside_menu_wrapper"
          data-kt-scroll="true"
          data-kt-scroll-activate="{default: false, lg: true}"
          data-kt-scroll-height="auto"
          data-kt-scroll-dependencies="#kt_aside_logo, #kt_aside_footer"
          data-kt-scroll-wrappers="#kt_aside_menu"
          data-kt-scroll-offset="0"
        >
          <div
            className="menu menu-column menu-title-gray-800 menu-state-title-primary menu-state-icon-primary menu-state-bullet-primary menu-arrow-gray-500"
            id="#kt_aside_menu"
            data-kt-menu="true"
            data-kt-menu-expand="false"
          >
            {/* <div className="menu-item">
              <div className="menu-content pt-8 pb-2">
                <span className="menu-section text-muted text-uppercase fs-8 ls-1"></span>
              </div>
            </div> */}
            {/* MAPPING SIDEBAR LINKS */}
            {linkList.map((link) => (
              <div key={link.id}>
                {link.divider && <div className="side-divider"></div>}
                {link.subMenu ? (
                  <div
                    className="side-item side-dropdown-item"
                    onClick={() => toggleAccordion(link.id)}
                  >
                    <div className="d-flex align-items-center gap-4">
                      <span className="svg-icon side-icon">{link.icon}</span>
                      <span
                        className={`side-title ${
                          shrinkSidebar && "shrink__menu-item"
                        } ${link.isMenuOpen && "active"}`}
                      >
                        {link.name}
                      </span>
                    </div>
                    <FaChevronDown
                      className={`side-arrow ${
                        link.isMenuOpen && "side-arrow-rotate"
                      }`}
                    />
                  </div>
                ) : (
                  <Link href={link.href}>
                    <a
                      className={`${
                        link.routeName === "mail" ? "message-count-link" : ""
                      }`}
                    >
                      <div className="side-item">
                        <span className="svg-icon side-icon">{link.icon}</span>
                        <span
                          className={`side-title ${
                            shrinkSidebar && "shrink__menu-item"
                          } ${
                            router.query.PageWithForm === link.routeName
                              ? "active"
                              : ""
                          }`}
                        >
                          {link.name}
                        </span>
                      </div>
                      {link.routeName === "mail" ? (
                        <span className="message-count-item">
                          {messageCount}
                        </span>
                      ) : null}
                    </a>
                  </Link>
                )}

                {link.subMenu && (
                  <ul
                    className={`side-submenu ${
                      link.isMenuOpen && "side-submenu-open"
                    }`}
                  >
                    {link.subMenu.map((item, index) => (
                      <li className="" key={index}>
                        {item.onClick ? (
                          <a
                            className={`side__submenu-link ${
                              shrinkSidebar && "shrink__menu-item"
                            }`}
                            onClick={async () => {
                              if (item.routeName === "report-365") {
                                const Swal = require("sweetalert2");
                                try {
                                  const response = await apiClientProtected().post("/finances/export-student-finance-job");

                                  if (response.data.success) {
                                    Swal.fire({
                                      icon: "success",
                                      title: "წარმატება",
                                      text: response.data.message,
                                      confirmButtonText: "დახურვა"
                                    });
                                  } else {
                                    Swal.fire({
                                      icon: "error",
                                      title: "შეცდომა",
                                      text: response.data.message,
                                      confirmButtonText: "დახურვა"
                                    });
                                  }
                                } catch (error) {
                                  Swal.fire({
                                    icon: "error",
                                    title: "შეცდომა",
                                    text: error.response?.data?.message || "დაფიქსირდა შეცდომა",
                                    confirmButtonText: "დახურვა"
                                  });
                                }
                              }
                            }}
                            style={{ cursor: "pointer" }}
                          >
                            <span
                              className={`side-bullet ${
                                router.query.PageWithForm === item.routeName
                                  ? "active"
                                  : ""
                              }`}
                            ></span>
                            <span
                              className={`side-submenu-title ${
                                router.query.PageWithForm === item.routeName ||
                                router.pathname === item.href
                                  ? "active"
                                  : ""
                              }`}
                            >
                              {item.name}
                            </span>
                          </a>
                        ) : (
                          <Link href={item.href}>
                            <a
                              className={`side__submenu-link ${
                                shrinkSidebar && "shrink__menu-item"
                              }`}
                            >
                              <span
                                className={`side-bullet ${
                                  router.query.PageWithForm === item.routeName
                                    ? "active"
                                    : ""
                                }`}
                              ></span>
                              <span
                                className={`side-submenu-title ${
                                  router.query.PageWithForm === item.routeName ||
                                  router.pathname === item.href
                                    ? "active"
                                    : ""
                                }`}
                              >
                                {item.name}
                              </span>
                            </a>
                          </Link>
                        )}
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default Sidebar;
